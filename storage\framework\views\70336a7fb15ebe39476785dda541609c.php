<style>
    .sales-view {
        display: none;
    }

    .sales-view.active {
        display: block;
    }

    /* Customer search dropdown styling */
    #customerDropdown {
        position: absolute;
        top: 100%;
        left: 0;
        z-index: 1050;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        background-color: #fff;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        display: none;
    }

    #customerDropdown.show {
        display: block !important;
    }

    .customer-option {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #f8f9fa;
        text-decoration: none;
        color: #212529;
        display: block;
        cursor: pointer;
    }

    .customer-option:hover {
        background-color: #f8f9fa;
        color: #212529;
        text-decoration: none;
    }

    .customer-option:last-child {
        border-bottom: none;
    }

    #customerNameSearch {
        position: relative;
    }

    .position-relative {
        position: relative;
    }

    .dropdown-item-text {
        padding: 0.75rem 1rem;
        color: #6c757d;
    }

    /* Product selection enhancements */
    .product-select {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 0.75rem center;
        background-size: 16px 12px;
    }

    .product-details-card {
        transition: all 0.3s ease;
        border-left: 4px solid #0d6efd;
    }

    .product-details-card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .product-status-badge {
        animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Loading state for product dropdowns */
    .product-select option[value=""]:first-child {
        color: #6c757d;
        font-style: italic;
    }

    /* Enhanced product option styling */
    .product-select option {
        padding: 8px 12px;
    }

    /* Loading state for product selects */
    .product-select.loading {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23007bff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M8 2v4M8 10v4M2 8h4M10 8h4'/%3e%3c/svg%3e");
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Product price display in options */
    .product-select option[data-price]:not([data-price=""]):after {
        content: " - " attr(data-price);
        color: #28a745;
        font-weight: 500;
    }

    /* Enhanced product details card */
    #subscriptionProductDetailsSection .card,
    #installmentProductDetailsSection .card {
        transition: all 0.3s ease;
        border-left: 4px solid #0d6efd;
    }

    #subscriptionProductDetailsSection .card:hover,
    #installmentProductDetailsSection .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }
</style>

<!-- Sales Tab Content -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0"><?php echo e(__('Sales Management')); ?></h4>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-success active" data-sales-view="subscription">
                    <i class="ti ti-repeat me-1"></i><?php echo e(__('Subscription')); ?>

                </button>
                <button type="button" class="btn btn-outline-success" data-sales-view="installment">
                    <i class="ti ti-calendar-time me-1"></i><?php echo e(__('Installment')); ?>

                </button>
            </div>
        </div>
    </div>
</div>

<!-- Subscription View -->
<div id="subscription-view" class="sales-view active">
    <div class="row mb-4">
        <!-- Subscription Stats -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-primary text-white">
                <div class="card-body text-center">
                    <i class="ti ti-users" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">0</h4>
                    <small><?php echo e(__('Active Subscribers')); ?></small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-success text-white">
                <div class="card-body text-center">
                    <i class="ti ti-currency-dollar" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1"><?php echo e(\Auth::user()->priceFormat(0)); ?></h4>
                    <small><?php echo e(__('Monthly Recurring Revenue')); ?></small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-warning text-white">
                <div class="card-body text-center">
                    <i class="ti ti-clock" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">0</h4>
                    <small><?php echo e(__('Pending Subscriptions')); ?></small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-info text-white">
                <div class="card-body text-center">
                    <i class="ti ti-percentage" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">0%</h4>
                    <small><?php echo e(__('Churn Rate')); ?></small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('Subscription Plans')); ?></h5>
                    <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#createSubscriptionModal">
                        <i class="ti ti-plus me-1"></i><?php echo e(__('Create Plan')); ?>

                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="subscriptionsTable">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('ID')); ?></th>
                                    <th><?php echo e(__('Status / Created At')); ?></th>
                                    <th><?php echo e(__('Next EMI Date')); ?></th>
                                    <th><?php echo e(__('Customer Name / Email / Phone')); ?></th>
                                    <th><?php echo e(__('Product')); ?></th>
                                    <th><?php echo e(__('Product Price / Downpayment')); ?></th>
                                    <th><?php echo e(__('Paid Amount / Pending Amount')); ?></th>
                                    <th><?php echo e(__('Discount Amount')); ?></th>
                                    <th><?php echo e(__('Receipt')); ?></th>
                                    <th><?php echo e(__('Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                    try {
                                        $subscriptions = \App\Models\Subscription::where('created_by', \Auth::user()->creatorId())->orderBy('created_at', 'desc')->get();
                                    } catch (\Exception $e) {
                                        $subscriptions = collect([]);
                                    }
                                ?>
                                <?php $__empty_1 = true; $__currentLoopData = $subscriptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subscription): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <strong><?php echo e($subscription->subscription_id); ?></strong>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo e($subscription->getStatusBadgeClass()); ?> mb-1">
                                            <?php echo e($subscription->getFormattedStatus()); ?>

                                        </span>
                                        <br>
                                        <small class="text-muted"><?php echo e($subscription->created_at->format('M d, Y')); ?></small>
                                    </td>
                                    <td>
                                        <?php if($subscription->next_emi_date): ?>
                                            <span class="text-<?php echo e($subscription->isOverdue() ? 'danger' : 'primary'); ?>">
                                                <?php echo e($subscription->next_emi_date->format('M d, Y')); ?>

                                            </span>
                                            <?php if($subscription->isOverdue()): ?>
                                                <br><small class="text-danger"><?php echo e(__('Overdue')); ?></small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted"><?php echo e(__('N/A')); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo e($subscription->customer_name); ?></strong><br>
                                            <small class="text-muted"><?php echo e($subscription->customer_email); ?></small><br>
                                            <?php if($subscription->customer_phone): ?>
                                                <small class="text-muted"><?php echo e($subscription->customer_phone); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-medium"><?php echo e($subscription->product_name); ?></span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo e(\Auth::user()->priceFormat($subscription->product_price)); ?></strong><br>
                                            <small class="text-muted"><?php echo e(__('Down:')); ?> <?php echo e(\Auth::user()->priceFormat($subscription->down_payment)); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <span class="text-success"><?php echo e(\Auth::user()->priceFormat($subscription->paid_amount)); ?></span><br>
                                            <span class="text-warning"><?php echo e(\Auth::user()->priceFormat($subscription->pending_amount)); ?></span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-info"><?php echo e(\Auth::user()->priceFormat($subscription->discount_amount)); ?></span>
                                    </td>
                                    <td>
                                        <?php if($subscription->receipt_url): ?>
                                            <a href="<?php echo e($subscription->receipt_url); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="ti ti-file-text"></i>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted"><?php echo e(__('N/A')); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="ti ti-dots"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="cancelSubscription(<?php echo e($subscription->id); ?>)">
                                                        <i class="ti ti-x me-2"></i><?php echo e(__('Cancel Subscription')); ?>

                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="showSubscriptionNotes(<?php echo e($subscription->id); ?>)">
                                                        <i class="ti ti-notes me-2"></i><?php echo e(__('Subscription Notes')); ?>

                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="openWhatsAppChat('<?php echo e($subscription->customer_phone); ?>')">
                                                        <i class="ti ti-brand-whatsapp me-2"></i><?php echo e(__('WhatsApp Chat')); ?>

                                                    </a>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="editSubscription(<?php echo e($subscription->id); ?>)">
                                                        <i class="ti ti-edit me-2"></i><?php echo e(__('Edit')); ?>

                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="#" onclick="deleteSubscription(<?php echo e($subscription->id); ?>)">
                                                        <i class="ti ti-trash me-2"></i><?php echo e(__('Delete')); ?>

                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="10" class="text-center py-5">
                                        <div class="text-muted">
                                            <i class="ti ti-repeat" style="font-size: 3rem;"></i>
                                            <h5 class="mt-3"><?php echo e(__('No Subscription Plans')); ?></h5>
                                            <p><?php echo e(__('Create your first subscription plan to start recurring billing')); ?></p>
                                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createSubscriptionModal">
                                                <i class="ti ti-plus me-1"></i><?php echo e(__('Create Plan')); ?>

                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Installment View -->
<div id="installment-view" class="sales-view" style="display: none;">
    <div class="row mb-4">
        <!-- Installment Stats -->
        <?php
            try {
                $activePlans = \App\Models\InstallmentPlan::where('created_by', \Auth::user()->creatorId())->where('status', 'active')->count();
                $totalValue = \App\Models\InstallmentPlan::where('created_by', \Auth::user()->creatorId())->sum('total_amount');
                $overduePlans = \App\Models\InstallmentPlan::where('created_by', \Auth::user()->creatorId())->where('status', 'overdue')->count();
                $completedPlans = \App\Models\InstallmentPlan::where('created_by', \Auth::user()->creatorId())->where('status', 'completed')->count();
                $totalPlans = \App\Models\InstallmentPlan::where('created_by', \Auth::user()->creatorId())->count();
                $collectionRate = $totalPlans > 0 ? round(($completedPlans / $totalPlans) * 100, 1) : 0;
            } catch (\Exception $e) {
                $activePlans = 0;
                $totalValue = 0;
                $overduePlans = 0;
                $collectionRate = 0;
            }
        ?>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-primary text-white">
                <div class="card-body text-center">
                    <i class="ti ti-calendar-dollar" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1"><?php echo e($activePlans); ?></h4>
                    <small><?php echo e(__('Active Plans')); ?></small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-success text-white">
                <div class="card-body text-center">
                    <i class="ti ti-currency-dollar" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1"><?php echo e(\Auth::user()->priceFormat($totalValue)); ?></h4>
                    <small><?php echo e(__('Total Value')); ?></small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-warning text-white">
                <div class="card-body text-center">
                    <i class="ti ti-clock-exclamation" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1"><?php echo e($overduePlans); ?></h4>
                    <small><?php echo e(__('Overdue Payments')); ?></small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-info text-white">
                <div class="card-body text-center">
                    <i class="ti ti-percentage" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1"><?php echo e($collectionRate); ?>%</h4>
                    <small><?php echo e(__('Collection Rate')); ?></small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('Installment Plans')); ?></h5>
                    <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#createInstallmentModal">
                        <i class="ti ti-plus me-1"></i><?php echo e(__('Create Plan')); ?>

                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="installmentPlansTable">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('ID')); ?></th>
                                    <th><?php echo e(__('Status / Created At')); ?></th>
                                    <th><?php echo e(__('Next Installment Date')); ?></th>
                                    <th><?php echo e(__('Customer Name / Email / Phone')); ?></th>
                                    <th><?php echo e(__('Product')); ?></th>
                                    <th><?php echo e(__('Product Price / Downpayment')); ?></th>
                                    <th><?php echo e(__('Paid Amount / Installment Amount')); ?></th>
                                    <th><?php echo e(__('Discount Amount')); ?></th>
                                    <th><?php echo e(__('Receipt')); ?></th>
                                    <th><?php echo e(__('Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                    try {
                                        $installmentPlans = \App\Models\InstallmentPlan::where('created_by', \Auth::user()->creatorId())->orderBy('created_at', 'desc')->get();
                                    } catch (\Exception $e) {
                                        $installmentPlans = collect([]);
                                    }
                                ?>
                                <?php $__empty_1 = true; $__currentLoopData = $installmentPlans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <strong><?php echo e($plan->plan_id); ?></strong>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo e($plan->getStatusBadgeClass()); ?> mb-1">
                                            <?php echo e($plan->getFormattedStatus()); ?>

                                        </span>
                                        <br>
                                        <small class="text-muted"><?php echo e($plan->created_at->format('M d, Y')); ?></small>
                                    </td>
                                    <td>
                                        <?php if($plan->next_installment_date): ?>
                                            <span class="text-<?php echo e($plan->isOverdue() ? 'danger' : 'primary'); ?>">
                                                <?php echo e($plan->next_installment_date->format('M d, Y')); ?>

                                            </span>
                                            <?php if($plan->isOverdue()): ?>
                                                <br><small class="text-danger"><?php echo e(__('Overdue')); ?></small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted"><?php echo e(__('N/A')); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo e($plan->customer_name); ?></strong><br>
                                            <small class="text-muted"><?php echo e($plan->customer_email); ?></small><br>
                                            <?php if($plan->customer_phone): ?>
                                                <small class="text-muted"><?php echo e($plan->customer_phone); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-medium"><?php echo e($plan->product_name); ?></span>
                                        <?php if($plan->quantity > 1): ?>
                                            <br><small class="text-muted"><?php echo e(__('Qty:')); ?> <?php echo e($plan->quantity); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo e(\Auth::user()->priceFormat($plan->product_price)); ?></strong><br>
                                            <small class="text-muted"><?php echo e(__('Down:')); ?> <?php echo e(\Auth::user()->priceFormat($plan->down_payment)); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <span class="text-success"><?php echo e(\Auth::user()->priceFormat($plan->paid_amount)); ?></span><br>
                                            <span class="text-info"><?php echo e(\Auth::user()->priceFormat($plan->installment_amount)); ?></span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-info"><?php echo e(\Auth::user()->priceFormat($plan->discount_amount)); ?></span>
                                    </td>
                                    <td>
                                        <?php if($plan->receipt_url): ?>
                                            <a href="<?php echo e($plan->receipt_url); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="ti ti-file-text"></i>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted"><?php echo e(__('N/A')); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="ti ti-dots"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="cancelInstallmentPlan(<?php echo e($plan->id); ?>)">
                                                        <i class="ti ti-x me-2"></i><?php echo e(__('Cancel Subscription')); ?>

                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="showInstallmentPlanNotes(<?php echo e($plan->id); ?>)">
                                                        <i class="ti ti-notes me-2"></i><?php echo e(__('Subscription Notes')); ?>

                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="openWhatsAppChat('<?php echo e($plan->customer_phone); ?>')">
                                                        <i class="ti ti-brand-whatsapp me-2"></i><?php echo e(__('WhatsApp Chat')); ?>

                                                    </a>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="editInstallmentPlan(<?php echo e($plan->id); ?>)">
                                                        <i class="ti ti-edit me-2"></i><?php echo e(__('Edit')); ?>

                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="#" onclick="deleteInstallmentPlan(<?php echo e($plan->id); ?>)">
                                                        <i class="ti ti-trash me-2"></i><?php echo e(__('Delete')); ?>

                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="10" class="text-center py-5">
                                        <div class="text-muted">
                                            <i class="ti ti-calendar-time" style="font-size: 3rem;"></i>
                                            <h5 class="mt-3"><?php echo e(__('No Installment Plans')); ?></h5>
                                            <p><?php echo e(__('Create installment plans for large purchases')); ?></p>
                                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createInstallmentModal">
                                                <i class="ti ti-plus me-1"></i><?php echo e(__('Create Plan')); ?>

                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><?php echo e(__('Quick Actions')); ?></h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="<?php echo e(route('customer.index')); ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-users mb-2" style="font-size: 1.5rem;"></i>
                            <span><?php echo e(__('Customers')); ?></span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="<?php echo e(route('invoice.index')); ?>" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-file-invoice mb-2" style="font-size: 1.5rem;"></i>
                            <span><?php echo e(__('Invoices')); ?></span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="<?php echo e(route('productservice.index')); ?>" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-package mb-2" style="font-size: 1.5rem;"></i>
                            <span><?php echo e(__('Products')); ?></span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="<?php echo e(route('report.income.summary')); ?>" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-chart-line mb-2" style="font-size: 1.5rem;"></i>
                            <span><?php echo e(__('Reports')); ?></span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3" data-bs-toggle="modal" data-bs-target="#createSubscriptionModal">
                            <i class="ti ti-repeat mb-2" style="font-size: 1.5rem;"></i>
                            <span><?php echo e(__('New Subscription')); ?></span>
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3" data-bs-toggle="modal" data-bs-target="#createInstallmentModal">
                            <i class="ti ti-calendar-time mb-2" style="font-size: 1.5rem;"></i>
                            <span><?php echo e(__('New Installment')); ?></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Subscription Modal -->
<div class="modal fade" id="createSubscriptionModal" tabindex="-1" aria-labelledby="createSubscriptionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createSubscriptionModalLabel"><?php echo e(__('Create Subscription Plan')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createSubscriptionForm">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <!-- Customer Details Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0 text-primary"><?php echo e(__('Customer Details')); ?></h6>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="customerName" class="form-label"><?php echo e(__('Customer Name')); ?></label>
                            <div class="position-relative">
                                <input type="text" class="form-control" id="customerNameSearch" placeholder="<?php echo e(__('Search customer name...')); ?>" autocomplete="off">
                                <input type="hidden" id="customerName" name="customer_id" required>
                                <input type="hidden" id="customerType" name="customer_type">
                                <div class="dropdown-menu w-100" id="customerDropdown" style="max-height: 200px; overflow-y: auto;">
                                    <!-- Dynamic customer options will be loaded here -->
                                </div>
                            </div>
                            <div class="invalid-feedback"></div>
                            <small class="text-muted"><?php echo e(__('Search from Customers and Leads')); ?></small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="customerEmail" class="form-label"><?php echo e(__('Customer Email')); ?></label>
                            <select class="form-select" id="customerEmail" name="customer_email" required>
                                <option value=""><?php echo e(__('Select Customer Email')); ?></option>
                            </select>
                            <div class="invalid-feedback"></div>
                            <small class="text-muted" id="emailHelpText"><?php echo e(__('Auto-populated from selected customer')); ?></small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="customerContact" class="form-label"><?php echo e(__('Customer Contact')); ?></label>
                            <select class="form-select" id="customerContact" name="customer_phone">
                                <option value=""><?php echo e(__('Select Customer Contact')); ?></option>
                            </select>
                            <small class="text-muted" id="phoneHelpText"><?php echo e(__('Auto-populated from selected customer')); ?></small>
                        </div>
                    </div>

                    <!-- Product Details Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0 text-primary"><?php echo e(__('Product Details')); ?></h6>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-success btn-sm"
                                            data-bs-toggle="tooltip"
                                            data-bs-placement="top"
                                            title="<?php echo e(__('Add New Product')); ?>"
                                            onclick="window.open('<?php echo e(route('productservice.create')); ?>', '_blank')">
                                        <i class="ti ti-plus"></i>
                                    </button>
                                    <button type="button" class="btn btn-info btn-sm"
                                            data-bs-toggle="tooltip"
                                            data-bs-placement="top"
                                            title="<?php echo e(__('View All Products')); ?>"
                                            onclick="window.open('<?php echo e(route('productservice.index')); ?>', '_blank')">
                                        <i class="ti ti-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="product" class="form-label"><?php echo e(__('Product')); ?> <span class="text-danger">*</span></label>
                            <div class="position-relative">
                                <select class="form-select subscription-product-select product-select" id="product" name="product_id" required>
                                    <option value=""><?php echo e(__('Loading products...')); ?></option>
                                </select>
                                <div class="position-absolute top-50 end-0 translate-middle-y me-3" style="pointer-events: none;">
                                    <i class="ti ti-package text-muted"></i>
                                </div>
                            </div>
                            <div class="invalid-feedback"></div>
                            <small class="text-muted">
                                <i class="ti ti-info-circle me-1"></i><?php echo e(__('Select a product for this subscription plan')); ?>

                            </small>
                        </div>
                        <div class="col-md-6 mb-3" id="subscriptionProductDetailsSection" style="display: none;">
                            <div class="card bg-light border-primary">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="card-title mb-0 text-primary">
                                            <i class="ti ti-info-circle me-1"></i><?php echo e(__('Product Details')); ?>

                                        </h6>
                                        <span class="badge bg-success" id="subscriptionProductStatus"><?php echo e(__('Selected')); ?></span>
                                    </div>
                                    <div class="row">
                                        <div class="col-6 mb-2">
                                            <small class="text-muted d-block"><?php echo e(__('SKU/Code')); ?>:</small>
                                            <div id="subscriptionProductSku" class="fw-medium text-dark">-</div>
                                        </div>
                                        <div class="col-6 mb-2">
                                            <small class="text-muted d-block"><?php echo e(__('Category')); ?>:</small>
                                            <div id="subscriptionProductCategory" class="fw-medium text-dark">-</div>
                                        </div>
                                        <div class="col-12 mt-1">
                                            <small class="text-muted d-block"><?php echo e(__('Description')); ?>:</small>
                                            <div id="subscriptionProductDescription" class="text-sm text-dark" style="max-height: 60px; overflow-y: auto;">-</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="productQty" class="form-label"><?php echo e(__('Product Qty')); ?></label>
                            <input type="number" class="form-control" id="productQty" name="quantity" value="1" min="1">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="activeDate" class="form-label"><?php echo e(__('Active Date')); ?></label>
                            <input type="date" class="form-control" id="activeDate" name="start_date" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="productPrice" class="form-label"><?php echo e(__('Product Price')); ?></label>
                            <input type="number" step="0.01" class="form-control" id="productPrice" name="product_price" placeholder="<?php echo e(__('Product Price')); ?>" readonly>
                        </div>
                        <div class="col-12 mb-3">
                            <label for="invoiceDescription" class="form-label"><?php echo e(__('Invoice Description')); ?></label>
                            <textarea class="form-control" id="invoiceDescription" name="description" rows="3" placeholder="<?php echo e(__('Invoice Description')); ?>"></textarea>
                        </div>
                    </div>

                    <!-- Payment Details Section -->
                    <div class="row mb-4">
                        <div class="col-12 mb-3">
                            <h6 class="mb-3 text-primary"><?php echo e(__('Payment Method')); ?></h6>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="payment_method" id="offline" value="offline" checked>
                                <label class="form-check-label" for="offline">
                                    <?php echo e(__('Offline')); ?>

                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="payment_method" id="online" value="online">
                                <label class="form-check-label" for="online">
                                    <?php echo e(__('Online')); ?>

                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="downPayment" class="form-label"><?php echo e(__('Down Payment')); ?></label>
                            <input type="number" step="0.01" class="form-control" id="downPayment" name="down_payment" placeholder="0.00">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="discountAmount" class="form-label"><?php echo e(__('Discount Amount')); ?></label>
                            <input type="number" step="0.01" class="form-control" id="discountAmount" name="discount_amount" placeholder="0.00">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="totalEmis" class="form-label"><?php echo e(__('Total EMIs')); ?></label>
                            <input type="number" class="form-control" id="totalEmis" name="total_emis" placeholder="12" min="1" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="billingCycle" class="form-label"><?php echo e(__('Billing Cycle')); ?></label>
                            <select class="form-select" id="billingCycle" name="billing_cycle" required>
                                <option value="monthly"><?php echo e(__('Monthly')); ?></option>
                                <option value="quarterly"><?php echo e(__('Quarterly')); ?></option>
                                <option value="yearly"><?php echo e(__('Yearly')); ?></option>
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                    <button type="submit" class="btn btn-success"><?php echo e(__('Create Subscription')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create Installment Modal -->
<div class="modal fade" id="createInstallmentModal" tabindex="-1" aria-labelledby="createInstallmentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createInstallmentModalLabel"><?php echo e(__('Create Installment Plan')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createInstallmentForm">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <!-- Customer Details Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0 text-primary"><?php echo e(__('Customer Details')); ?></h6>
                                <button type="button" class="btn btn-success btn-sm" id="addCustomerBtn">
                                    <?php echo e(__('Add')); ?>

                                </button>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="installmentCustomerName" class="form-label"><?php echo e(__('Customer Name')); ?></label>
                            <div class="position-relative">
                                <input type="text" class="form-control" id="installmentCustomerNameSearch" placeholder="<?php echo e(__('Search customer name...')); ?>" autocomplete="off">
                                <input type="hidden" id="installmentCustomerName" name="customer_id" required>
                                <input type="hidden" id="installmentCustomerType" name="customer_type">
                                <div class="dropdown-menu w-100" id="installmentCustomerDropdown" style="max-height: 200px; overflow-y: auto;">
                                    <!-- Dynamic customer options will be loaded here -->
                                </div>
                            </div>
                            <div class="invalid-feedback"></div>
                            <small class="text-muted"><?php echo e(__('Search from Customers and Leads')); ?></small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="installmentCustomerEmail" class="form-label"><?php echo e(__('Customer Email')); ?></label>
                            <select class="form-select" id="installmentCustomerEmail" name="customer_email" required>
                                <option value=""><?php echo e(__('Select Customer Email')); ?></option>
                            </select>
                            <div class="invalid-feedback"></div>
                            <small class="text-muted" id="installmentEmailHelpText"><?php echo e(__('Auto-populated from selected customer')); ?></small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="installmentCustomerContact" class="form-label"><?php echo e(__('Customer Contact')); ?></label>
                            <select class="form-select" id="installmentCustomerContact" name="customer_phone">
                                <option value=""><?php echo e(__('Select Customer Contact')); ?></option>
                            </select>
                            <small class="text-muted" id="installmentPhoneHelpText"><?php echo e(__('Auto-populated from selected customer')); ?></small>
                        </div>
                    </div>

                    <!-- Product Details Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0 text-primary"><?php echo e(__('Product Details')); ?></h6>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-success btn-sm"
                                            data-bs-toggle="tooltip"
                                            data-bs-placement="top"
                                            title="<?php echo e(__('Add New Product')); ?>"
                                            onclick="window.open('<?php echo e(route('productservice.create')); ?>', '_blank')">
                                        <i class="ti ti-plus"></i>
                                    </button>
                                    <button type="button" class="btn btn-info btn-sm"
                                            data-bs-toggle="tooltip"
                                            data-bs-placement="top"
                                            title="<?php echo e(__('View All Products')); ?>"
                                            onclick="window.open('<?php echo e(route('productservice.index')); ?>', '_blank')">
                                        <i class="ti ti-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="installmentProduct" class="form-label"><?php echo e(__('Product')); ?> <span class="text-danger">*</span></label>
                            <div class="position-relative">
                                <select class="form-select installment-product-select product-select" id="installmentProduct" name="product_id" required>
                                    <option value=""><?php echo e(__('Loading products...')); ?></option>
                                </select>
                                <div class="position-absolute top-50 end-0 translate-middle-y me-3" style="pointer-events: none;">
                                    <i class="ti ti-package text-muted"></i>
                                </div>
                            </div>
                            <div class="invalid-feedback"></div>
                            <small class="text-muted">
                                <i class="ti ti-info-circle me-1"></i><?php echo e(__('Select a product for this installment plan')); ?>

                            </small>
                        </div>
                        <div class="col-md-6 mb-3" id="installmentProductDetailsSection" style="display: none;">
                            <div class="card bg-light border-primary">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="card-title mb-0 text-primary">
                                            <i class="ti ti-info-circle me-1"></i><?php echo e(__('Product Details')); ?>

                                        </h6>
                                        <span class="badge bg-success" id="installmentProductStatus"><?php echo e(__('Selected')); ?></span>
                                    </div>
                                    <div class="row">
                                        <div class="col-6 mb-2">
                                            <small class="text-muted d-block"><?php echo e(__('SKU/Code')); ?>:</small>
                                            <div id="installmentProductSku" class="fw-medium text-dark">-</div>
                                        </div>
                                        <div class="col-6 mb-2">
                                            <small class="text-muted d-block"><?php echo e(__('Category')); ?>:</small>
                                            <div id="installmentProductCategory" class="fw-medium text-dark">-</div>
                                        </div>
                                        <div class="col-12 mt-1">
                                            <small class="text-muted d-block"><?php echo e(__('Description')); ?>:</small>
                                            <div id="installmentProductDescription" class="text-sm text-dark" style="max-height: 60px; overflow-y: auto;">-</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="installmentProductQty" class="form-label"><?php echo e(__('Product Qty')); ?></label>
                            <input type="number" class="form-control" id="installmentProductQty" name="quantity" value="1" min="1">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="installmentActiveDate" class="form-label"><?php echo e(__('Active Date')); ?></label>
                            <input type="date" class="form-control" id="installmentActiveDate" name="start_date" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="installmentProductPrice" class="form-label"><?php echo e(__('Product Price')); ?></label>
                            <input type="number" step="0.01" class="form-control" id="installmentProductPrice" name="product_price" placeholder="<?php echo e(__('Product Price')); ?>" readonly>
                        </div>
                    </div>

                    <!-- Payment Details Section -->
                    <div class="row mb-4">
                        <div class="col-12 mb-3">
                            <h6 class="mb-3 text-primary"><?php echo e(__('Payment Method')); ?></h6>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="payment_method" id="installmentOffline" value="offline" checked>
                                <label class="form-check-label" for="installmentOffline">
                                    <?php echo e(__('Offline')); ?>

                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="payment_method" id="installmentOnline" value="online">
                                <label class="form-check-label" for="installmentOnline">
                                    <?php echo e(__('Online')); ?>

                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="installmentDownPayment" class="form-label"><?php echo e(__('Down Payment')); ?></label>
                            <input type="number" step="0.01" class="form-control" id="installmentDownPayment" name="down_payment" placeholder="0.00">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="installmentDiscountAmount" class="form-label"><?php echo e(__('Discount Amount')); ?></label>
                            <input type="number" step="0.01" class="form-control" id="installmentDiscountAmount" name="discount_amount" placeholder="0.00">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="installmentPaymentFrequency" class="form-label"><?php echo e(__('Payment Frequency')); ?></label>
                            <select class="form-select" id="installmentPaymentFrequency" name="payment_frequency" required>
                                <option value="monthly"><?php echo e(__('Monthly')); ?></option>
                                <option value="weekly"><?php echo e(__('Weekly')); ?></option>
                                <option value="quarterly"><?php echo e(__('Quarterly')); ?></option>
                                <option value="yearly"><?php echo e(__('Yearly')); ?></option>
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label"><?php echo e(__('Balance')); ?></label>
                            <div class="d-flex align-items-center">
                                <span id="installmentBalance" class="fw-bold text-primary">0</span>
                                <button type="button" class="btn btn-success btn-sm ms-auto" id="addInstallmentBtn">
                                    <?php echo e(__('Add Installment')); ?>

                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Installment Details Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="mb-3 text-primary"><?php echo e(__('Installment Details')); ?></h6>
                            <div id="installmentsList">
                                <!-- Dynamic installments will be added here -->
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Description Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <label for="installmentInvoiceDescription" class="form-label"><?php echo e(__('Invoice Description')); ?></label>
                            <textarea class="form-control" id="installmentInvoiceDescription" name="description" rows="4" placeholder="<?php echo e(__('Enter your content...')); ?>"></textarea>
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="checkbox" id="installmentDeclaration" checked>
                                <label class="form-check-label" for="installmentDeclaration">
                                    <?php echo e(__('I hereby declare that I\'m creating this invoice to collect online payment on the behalf of the organisation.')); ?>

                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                    <button type="submit" class="btn btn-success"><?php echo e(__('Create Installment Plan')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Subscription Notes Modal -->
<div class="modal fade" id="subscriptionNotesModal" tabindex="-1" aria-labelledby="subscriptionNotesModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="subscriptionNotesModalLabel"><?php echo e(__('Subscription Notes')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="subscriptionNotesForm">
                    <input type="hidden" id="notesSubscriptionId" name="subscription_id">
                    <div class="mb-3">
                        <label for="subscriptionNotes" class="form-label"><?php echo e(__('Notes')); ?></label>
                        <textarea class="form-control" id="subscriptionNotes" name="notes" rows="5" placeholder="<?php echo e(__('Enter subscription notes...')); ?>"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                <button type="button" class="btn btn-primary" onclick="saveSubscriptionNotes()"><?php echo e(__('Save Notes')); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Subscription Modal -->
<div class="modal fade" id="editSubscriptionModal" tabindex="-1" aria-labelledby="editSubscriptionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editSubscriptionModalLabel"><?php echo e(__('Edit Subscription')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editSubscriptionForm">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="editSubscriptionId" name="subscription_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="editStatus" class="form-label"><?php echo e(__('Status')); ?></label>
                            <select class="form-select" id="editStatus" name="status">
                                <option value="active"><?php echo e(__('Active')); ?></option>
                                <option value="paused"><?php echo e(__('Paused')); ?></option>
                                <option value="cancelled"><?php echo e(__('Cancelled')); ?></option>
                                <option value="expired"><?php echo e(__('Expired')); ?></option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editNextEmiDate" class="form-label"><?php echo e(__('Next EMI Date')); ?></label>
                            <input type="date" class="form-control" id="editNextEmiDate" name="next_emi_date">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editPaidAmount" class="form-label"><?php echo e(__('Paid Amount')); ?></label>
                            <input type="number" step="0.01" class="form-control" id="editPaidAmount" name="paid_amount">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editPendingAmount" class="form-label"><?php echo e(__('Pending Amount')); ?></label>
                            <input type="number" step="0.01" class="form-control" id="editPendingAmount" name="pending_amount">
                        </div>
                        <div class="col-12 mb-3">
                            <label for="editReceiptUrl" class="form-label"><?php echo e(__('Receipt URL')); ?></label>
                            <input type="url" class="form-control" id="editReceiptUrl" name="receipt_url" placeholder="https://...">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                    <button type="submit" class="btn btn-primary"><?php echo e(__('Update Subscription')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Installment Plan Notes Modal -->
<div class="modal fade" id="installmentPlanNotesModal" tabindex="-1" aria-labelledby="installmentPlanNotesModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="installmentPlanNotesModalLabel"><?php echo e(__('Installment Plan Notes')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="installmentPlanNotesForm">
                    <input type="hidden" id="notesInstallmentPlanId" name="installment_plan_id">
                    <div class="mb-3">
                        <label for="installmentPlanNotes" class="form-label"><?php echo e(__('Notes')); ?></label>
                        <textarea class="form-control" id="installmentPlanNotes" name="notes" rows="5" placeholder="<?php echo e(__('Enter installment plan notes...')); ?>"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                <button type="button" class="btn btn-primary" onclick="saveInstallmentPlanNotes()"><?php echo e(__('Save Notes')); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Installment Plan Modal -->
<div class="modal fade" id="editInstallmentPlanModal" tabindex="-1" aria-labelledby="editInstallmentPlanModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editInstallmentPlanModalLabel"><?php echo e(__('Edit Installment Plan')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editInstallmentPlanForm">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="editInstallmentPlanId" name="installment_plan_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="editInstallmentStatus" class="form-label"><?php echo e(__('Status')); ?></label>
                            <select class="form-select" id="editInstallmentStatus" name="status">
                                <option value="active"><?php echo e(__('Active')); ?></option>
                                <option value="paused"><?php echo e(__('Paused')); ?></option>
                                <option value="cancelled"><?php echo e(__('Cancelled')); ?></option>
                                <option value="completed"><?php echo e(__('Completed')); ?></option>
                                <option value="overdue"><?php echo e(__('Overdue')); ?></option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editInstallmentNextDate" class="form-label"><?php echo e(__('Next Installment Date')); ?></label>
                            <input type="date" class="form-control" id="editInstallmentNextDate" name="next_installment_date">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editInstallmentPaidAmount" class="form-label"><?php echo e(__('Paid Amount')); ?></label>
                            <input type="number" step="0.01" class="form-control" id="editInstallmentPaidAmount" name="paid_amount">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editInstallmentPendingAmount" class="form-label"><?php echo e(__('Pending Amount')); ?></label>
                            <input type="number" step="0.01" class="form-control" id="editInstallmentPendingAmount" name="pending_amount">
                        </div>
                        <div class="col-12 mb-3">
                            <label for="editInstallmentReceiptUrl" class="form-label"><?php echo e(__('Receipt URL')); ?></label>
                            <input type="url" class="form-control" id="editInstallmentReceiptUrl" name="receipt_url" placeholder="https://...">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                    <button type="submit" class="btn btn-primary"><?php echo e(__('Update Installment Plan')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sales view switching
    const salesViewButtons = document.querySelectorAll('[data-sales-view]');
    const salesViews = document.querySelectorAll('.sales-view');

    salesViewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetView = this.getAttribute('data-sales-view');

            // Remove active class from all buttons
            salesViewButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');

            // Hide all views
            salesViews.forEach(view => view.style.display = 'none');
            // Show target view
            const targetElement = document.getElementById(targetView + '-view');
            if (targetElement) {
                targetElement.style.display = 'block';
            }
        });
    });

    // Customer search functionality
    let searchTimeout;
    let selectedCustomer = null;

    $('#customerNameSearch').on('input', function() {
        const searchTerm = $(this).val();

        clearTimeout(searchTimeout);

        if (searchTerm.length < 2) {
            $('#customerDropdown').removeClass('show').empty();
            return;
        }

        searchTimeout = setTimeout(function() {
            searchCustomers(searchTerm);
        }, 300);
    });
    
    $('#installmentCustomerNameSearch').on('input', function() {
        const searchTerm = $(this).val();

        clearTimeout(searchTimeout);

        if (searchTerm.length < 2) {
            $('#installmentCustomerDropdown').removeClass('show').empty();
            return;
        }

        searchTimeout = setTimeout(function() {
            searchCustomers(searchTerm);
        }, 300);
    });

    // Handle customer search
    function searchCustomers(searchTerm) {
        $.ajax({
            url: '<?php echo e(route("finance.sales.search-contacts")); ?>',
            type: 'GET',
            data: { search: searchTerm },
            success: function(response) {
                if (response.success) {
                    displayCustomerOptions(response.contacts);
                } else {
                    $('#customerDropdown').removeClass('show').empty();
                }
            },
            error: function() {
                $('#customerDropdown').removeClass('show').empty();
            }
        });
    }

    // Display customer search results
    function displayCustomerOptions(contacts) {
        const dropdown = $('#customerDropdown');
        dropdown.empty();

        if (contacts.length === 0) {
            dropdown.append('<div class="dropdown-item-text text-muted"><?php echo e(__("No contacts found")); ?></div>');
        } else {
            contacts.forEach(function(contact) {
                const item = $(`
                    <a class="dropdown-item customer-option" href="#"
                       data-id="${contact.id}"
                       data-type="${contact.type}"
                       data-name="${contact.name}"
                       data-email="${contact.email}"
                       data-phone="${contact.phone}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>${contact.display_name}</strong>
                                <br>
                                <small class="text-muted">${contact.display_info}</small>
                            </div>
                            <span class="badge bg-${contact.type === 'customer' ? 'primary' : 'info'} ms-2">
                                ${contact.type === 'customer' ? '<?php echo e(__("Customer")); ?>' : '<?php echo e(__("Lead")); ?>'}
                            </span>
                        </div>
                    </a>
                `);
                dropdown.append(item);
            });
        }

        dropdown.addClass('show');
    }

    // Handle customer selection
    $(document).on('click', '.customer-option', function(e) {
        e.preventDefault();

        const customerId = $(this).data('id');
        const customerType = $(this).data('type');
        const customerName = $(this).data('name');

        // Set selected customer
        selectedCustomer = {
            id: customerId,
            type: customerType,
            name: customerName
        };

        // Update form fields
        $('#customerNameSearch').val(customerName);
        $('#customerName').val(customerId);
        $('#customerType').val(customerType);
        $('#customerDropdown').removeClass('show').empty();

        // Fetch detailed contact information
        fetchContactDetails(customerType, customerId);
    });

    // Fetch detailed contact information
    function fetchContactDetails(type, id) {
        $.ajax({
            url: `<?php echo e(route("finance.sales.get-contact-details", ["type" => ":type", "id" => ":id"])); ?>`
                .replace(':type', type)
                .replace(':id', id),
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    populateContactFields(response.contact);
                } else {
                    show_toastr('error', response.message || '<?php echo e(__("Failed to fetch contact details")); ?>');
                }
            },
            error: function() {
                show_toastr('error', '<?php echo e(__("Failed to fetch contact details")); ?>');
            }
        });
    }

    // Populate email and phone fields
    function populateContactFields(contact) {
        // Clear and populate email dropdown
        const emailSelect = $('#customerEmail');
        emailSelect.empty().append('<option value=""><?php echo e(__("Select Customer Email")); ?></option>');

        if (contact.emails && contact.emails.length > 0) {
            contact.emails.forEach(function(email) {
                if (email) {
                    emailSelect.append(`<option value="${email}">${email}</option>`);
                }
            });
            // Auto-select first email
            emailSelect.val(contact.emails[0]);
        }

        // Clear and populate phone dropdown
        const phoneSelect = $('#customerContact');
        phoneSelect.empty().append('<option value=""><?php echo e(__("Select Customer Contact")); ?></option>');

        if (contact.phones && contact.phones.length > 0) {
            contact.phones.forEach(function(phone) {
                if (phone) {
                    phoneSelect.append(`<option value="${phone}">${phone}</option>`);
                }
            });
            // Auto-select first phone
            phoneSelect.val(contact.phones[0]);
        }

        // Update help text
        $('#emailHelpText').text(`<?php echo e(__("Loaded from")); ?> ${contact.type === 'customer' ? '<?php echo e(__("Customer")); ?>' : '<?php echo e(__("Lead")); ?>'}: ${contact.name}`);
        $('#phoneHelpText').text(`<?php echo e(__("Loaded from")); ?> ${contact.type === 'customer' ? '<?php echo e(__("Customer")); ?>' : '<?php echo e(__("Lead")); ?>'}: ${contact.name}`);
    }

    // Hide dropdown when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#customerNameSearch, #customerDropdown').length) {
            $('#customerDropdown').removeClass('show');
        }
    });

    // Clear customer selection when search field is cleared
    $('#customerNameSearch').on('keyup', function() {
        if ($(this).val() === '') {
            selectedCustomer = null;
            $('#customerName').val('');
            $('#customerType').val('');
            $('#customerEmail').empty().append('<option value=""><?php echo e(__("Select Customer Email")); ?></option>');
            $('#customerContact').empty().append('<option value=""><?php echo e(__("Select Customer Contact")); ?></option>');
            $('#emailHelpText').text('<?php echo e(__("Auto-populated from selected customer")); ?>');
            $('#phoneHelpText').text('<?php echo e(__("Auto-populated from selected customer")); ?>');
        }
    });

    // Load products when subscription modal is opened
    $('#createSubscriptionModal').on('show.bs.modal', function() {
        loadSubscriptionProducts();

        // Set default active date to today
        const today = new Date().toISOString().split('T')[0];
        $('#activeDate').val(today);
    });

    // Add product search functionality for subscription
    let subscriptionProductSearchTimeout;
    $('#product').on('focus', function() {
        if ($(this).find('option').length <= 1) {
            loadSubscriptionProducts();
        }
    });

    // Add keyboard search for subscription products
    $('#product').on('keyup', function(e) {
        if (e.key === 'Enter' || e.key === 'ArrowDown' || e.key === 'ArrowUp') return;

        clearTimeout(subscriptionProductSearchTimeout);
        const searchTerm = $(this).val();

        subscriptionProductSearchTimeout = setTimeout(() => {
            if (searchTerm.length >= 2) {
                searchSubscriptionProducts(searchTerm);
            } else if (searchTerm.length === 0) {
                loadSubscriptionProducts();
            }
        }, 300);
    });

    // Search products for subscription
    function searchSubscriptionProducts(searchTerm) {
        $.ajax({
            url: '<?php echo e(route("invoice.products.dropdown")); ?>',
            type: 'GET',
            data: { search: searchTerm },
            success: function(response) {
                if (response.status) {
                    populateSubscriptionProductDropdown(response.data);
                }
            }
        });
    }

    // Load products for subscription dropdown
    function loadSubscriptionProducts() {
        const select = $('.subscription-product-select');
        select.html('<option value=""><?php echo e(__("Loading products...")); ?></option>');

        $.ajax({
            url: '<?php echo e(route("invoice.products.dropdown")); ?>',
            type: 'GET',
            success: function(response) {
                if (response.status) {
                    populateSubscriptionProductDropdown(response.data);
                    if (response.data.length === 0) {
                        select.html('<option value=""><?php echo e(__("No products available")); ?></option>');
                        show_toastr('warning', '<?php echo e(__("No products found. Please add products first.")); ?>');
                    }
                } else {
                    select.html('<option value=""><?php echo e(__("Failed to load products")); ?></option>');
                    show_toastr('error', response.message || '<?php echo e(__("Failed to load products")); ?>');
                }
            },
            error: function() {
                select.html('<option value=""><?php echo e(__("Error loading products")); ?></option>');
                show_toastr('error', '<?php echo e(__("Error loading products")); ?>');
            }
        });
    }

    // Populate subscription product dropdown with enhanced data
    function populateSubscriptionProductDropdown(products) {
        const select = $('.subscription-product-select');
        select.html('<option value=""><?php echo e(__("Select Product")); ?></option>');

        products.forEach(product => {
            const displayName = product.name + (product.sku ? ` (${product.sku})` : '');
            const priceDisplay = product.sale_price ? ` - <?php echo e(\Auth::user()->currencySymbol()); ?>${parseFloat(product.sale_price).toFixed(2)}` : '';

            select.append(`<option value="${product.id}"
                data-price="${product.sale_price || 0}"
                data-name="${product.name}"
                data-sku="${product.sku || ''}"
                data-description="${product.description || ''}"
                data-category="${product.category || ''}"
                data-type="${product.type || ''}"
                data-table-source="${product.table_source || ''}"
                >${displayName}${priceDisplay}</option>`);
        });
    }

    // Enhanced subscription product selection handling
    $('#product').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const productId = $(this).val();
        const price = selectedOption.data('price') || 0;
        const name = selectedOption.data('name') || '';
        const description = selectedOption.data('description') || '';

        if (productId) {
            // Auto-populate price
            $('#productPrice').val(parseFloat(price).toFixed(2));

            // Auto-populate invoice description if empty
            if (!$('#invoiceDescription').val() && description) {
                $('#invoiceDescription').val(description);
            }

            // Calculate EMI amount
            calculateEmiAmount();

            // Load detailed product information
            loadProductDetails(productId, 'subscription');

            // Show success message
            show_toastr('success', `<?php echo e(__("Product selected")); ?>: ${name}`);
        } else {
            // Clear fields when no product selected
            $('#productPrice').val('');
            $('#invoiceDescription').val('');
            hideProductDetails('subscription');
        }
    });

    // Load detailed product information with loading state
    function loadProductDetails(productId, type) {
        // Show loading state
        if (type === 'subscription') {
            $('#subscriptionProductDetailsSection').show();
            $('#subscriptionProductSku').text('<?php echo e(__("Loading...")); ?>');
            $('#subscriptionProductCategory').text('<?php echo e(__("Loading...")); ?>');
            $('#subscriptionProductDescription').text('<?php echo e(__("Loading...")); ?>');
        } else if (type === 'installment') {
            $('#installmentProductDetailsSection').show();
            $('#installmentProductSku').text('<?php echo e(__("Loading...")); ?>');
            $('#installmentProductCategory').text('<?php echo e(__("Loading...")); ?>');
            $('#installmentProductDescription').text('<?php echo e(__("Loading...")); ?>');
        }

        $.ajax({
            url: '<?php echo e(route("invoice.product.details", ":id")); ?>'.replace(':id', productId),
            type: 'GET',
            success: function(response) {
                if (response.status) {
                    displayProductDetails(response.data, type);
                } else {
                    show_toastr('error', response.message || '<?php echo e(__("Error loading product details")); ?>');
                    hideProductDetails(type);
                }
            },
            error: function() {
                show_toastr('error', '<?php echo e(__("Error loading product details")); ?>');
                hideProductDetails(type);
            }
        });
    }

    // Display enhanced product details with better formatting
    function displayProductDetails(product, type) {
        const sku = product.sku || product.hsn_sac_no || product.nickname || '-';
        const categoryName = product.category ?
            (typeof product.category === 'object' ? product.category.name : product.category) : '-';
        const description = product.description || product.product_description || '-';
        const productType = product.type || 'Product';

        if (type === 'subscription') {
            $('#subscriptionProductSku').text(sku);
            $('#subscriptionProductCategory').text(categoryName);
            $('#subscriptionProductDescription').text(description);
            $('#subscriptionProductDetailsSection').show();

            // Add product type badge if available
            if (product.type) {
                const typeBadge = `<span class="badge bg-info ms-2">${productType}</span>`;
                $('#subscriptionProductCategory').html(categoryName + typeBadge);
            }
        } else if (type === 'installment') {
            $('#installmentProductSku').text(sku);
            $('#installmentProductCategory').text(categoryName);
            $('#installmentProductDescription').text(description);
            $('#installmentProductDetailsSection').show();

            // Add product type badge if available
            if (product.type) {
                const typeBadge = `<span class="badge bg-info ms-2">${productType}</span>`;
                $('#installmentProductCategory').html(categoryName + typeBadge);
            }
        }

        // Show success message for product details loaded
        console.log('Product details loaded:', product.name);
    }

    // Hide product details
    function hideProductDetails(type) {
        if (type === 'subscription') {
            $('#subscriptionProductDetailsSection').hide();
        } else if (type === 'installment') {
            $('#installmentProductDetailsSection').hide();
        }
    }

    // Calculate EMI amount when values change
    $('#productPrice, #downPayment, #discountAmount, #totalEmis').on('input', calculateEmiAmount);

    function calculateEmiAmount() {
        const productPrice = parseFloat($('#productPrice').val()) || 0;
        const downPayment = parseFloat($('#downPayment').val()) || 0;
        const discountAmount = parseFloat($('#discountAmount').val()) || 0;
        const totalEmis = parseInt($('#totalEmis').val()) || 1;

        const remainingAmount = productPrice - downPayment - discountAmount;
        const emiAmount = remainingAmount / totalEmis;

        // You can display this EMI amount somewhere if needed
        console.log('EMI Amount:', emiAmount);
    }

    // Handle subscription form submission
    $('#createSubscriptionForm').on('submit', function(e) {
        e.preventDefault();

        // Validate customer selection
        if (!selectedCustomer || !$('#customerName').val()) {
            show_toastr('error', '<?php echo e(__("Please select a customer")); ?>');
            $('#customerNameSearch').addClass('is-invalid');
            return;
        }

        const formData = new FormData(this);
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.text();

        submitBtn.prop('disabled', true).text('<?php echo e(__("Creating...")); ?>');

        $.ajax({
            url: '<?php echo e(route("finance.sales.store-subscription")); ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    show_toastr('success', response.message);
                    $('#createSubscriptionModal').modal('hide');
                    resetSubscriptionForm();
                    location.reload(); // Refresh to show new subscription
                } else {
                    show_toastr('error', response.message || '<?php echo e(__("Something went wrong")); ?>');
                }
            },
            error: function(xhr) {
                let errorMessage = '<?php echo e(__("Something went wrong")); ?>';

                // Clear previous validation errors
                $('.form-control, #customerNameSearch').removeClass('is-invalid');
                $('.invalid-feedback').text('');

                if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(function(field) {
                        let input = $(`[name="${field}"]`);

                        // Handle customer_id field specially
                        if (field === 'customer_id') {
                            input = $('#customerNameSearch');
                        }

                        const feedback = input.siblings('.invalid-feedback');

                        input.addClass('is-invalid');
                        if (feedback.length) {
                            feedback.text(errors[field][0]);
                        }
                    });
                    errorMessage = '<?php echo e(__("Please check the form for errors")); ?>';
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                show_toastr('error', errorMessage);
            },
            complete: function() {
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Reset subscription form with enhanced cleanup
    function resetSubscriptionForm() {
        $('#createSubscriptionForm')[0].reset();
        selectedCustomer = null;

        // Reset customer fields
        $('#customerNameSearch').val('');
        $('#customerName').val('');
        $('#customerType').val('');
        $('#customerEmail').empty().append('<option value=""><?php echo e(__("Select Customer Email")); ?></option>');
        $('#customerContact').empty().append('<option value=""><?php echo e(__("Select Customer Contact")); ?></option>');
        $('#emailHelpText').text('<?php echo e(__("Auto-populated from selected customer")); ?>');
        $('#phoneHelpText').text('<?php echo e(__("Auto-populated from selected customer")); ?>');

        // Reset product fields
        $('#product').html('<option value=""><?php echo e(__("Select Product")); ?></option>');
        $('#productPrice').val('');
        $('#invoiceDescription').val('');
        hideProductDetails('subscription');

        // Clear validation states
        $('.form-control, #customerNameSearch').removeClass('is-invalid');
        $('.invalid-feedback').text('');

        // Reload products for next use
        loadSubscriptionProducts();
    }

    // Reset modal when closed
    $('#createSubscriptionModal').on('hidden.bs.modal', function() {
        resetSubscriptionForm();
    });

    // Clear validation errors when user starts typing in customer search
    $('#customerNameSearch').on('input', function() {
        $(this).removeClass('is-invalid');
        $(this).siblings('.invalid-feedback').text('');
    });
});

// Subscription action functions
function cancelSubscription(subscriptionId) {
    if (confirm('Are you sure you want to cancel this subscription?')) {
        $.ajax({
            url: `<?php echo e(route("finance.sales.cancel-subscription", ":id")); ?>`.replace(':id', subscriptionId),
            type: 'POST',
            data: {
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                if (response.success) {
                    show_toastr('success', response.message);
                    location.reload();
                } else {
                    show_toastr('error', response.message);
                }
            },
            error: function() {
                show_toastr('error', 'Failed to cancel subscription');
            }
        });
    }
}

function showSubscriptionNotes(subscriptionId) {
    // Load existing notes
    $.ajax({
        url: `<?php echo e(route("finance.sales.get-subscription", ":id")); ?>`.replace(':id', subscriptionId),
        type: 'GET',
        success: function(response) {
            if (response.success) {
                $('#notesSubscriptionId').val(subscriptionId);
                $('#subscriptionNotes').val(response.subscription.notes || '');
                $('#subscriptionNotesModal').modal('show');
            }
        }
    });
}

function saveSubscriptionNotes() {
    const subscriptionId = $('#notesSubscriptionId').val();
    const notes = $('#subscriptionNotes').val();

    $.ajax({
        url: `<?php echo e(route("finance.sales.update-subscription-notes", ":id")); ?>`.replace(':id', subscriptionId),
        type: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>',
            notes: notes
        },
        success: function(response) {
            if (response.success) {
                show_toastr('success', response.message);
                $('#subscriptionNotesModal').modal('hide');
            } else {
                show_toastr('error', response.message);
            }
        },
        error: function() {
            show_toastr('error', 'Failed to save notes');
        }
    });
}

function openWhatsAppChat(phone) {
    if (!phone) {
        show_toastr('error', 'No phone number available');
        return;
    }

    const cleanPhone = phone.replace(/[^\d]/g, '');
    const whatsappUrl = `https://wa.me/${cleanPhone}`;
    window.open(whatsappUrl, '_blank');
}

function editSubscription(subscriptionId) {
    // Load subscription data
    $.ajax({
        url: `<?php echo e(route("finance.sales.get-subscription", ":id")); ?>`.replace(':id', subscriptionId),
        type: 'GET',
        success: function(response) {
            if (response.success) {
                const subscription = response.subscription;
                $('#editSubscriptionId').val(subscriptionId);
                $('#editStatus').val(subscription.status);
                $('#editNextEmiDate').val(subscription.next_emi_date);
                $('#editPaidAmount').val(subscription.paid_amount);
                $('#editPendingAmount').val(subscription.pending_amount);
                $('#editReceiptUrl').val(subscription.receipt_url);
                $('#editSubscriptionModal').modal('show');
            }
        }
    });
}

function deleteSubscription(subscriptionId) {
    if (confirm('Are you sure you want to delete this subscription? This action cannot be undone.')) {
        $.ajax({
            url: `<?php echo e(route("finance.sales.delete-subscription", ":id")); ?>`.replace(':id', subscriptionId),
            type: 'DELETE',
            data: {
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                if (response.success) {
                    show_toastr('success', response.message);
                    location.reload();
                } else {
                    show_toastr('error', response.message);
                }
            },
            error: function() {
                show_toastr('error', 'Failed to delete subscription');
            }
        });
    }
}

// Handle edit subscription form submission
$(document).on('submit', '#editSubscriptionForm', function(e) {
    e.preventDefault();

    const subscriptionId = $('#editSubscriptionId').val();
    const formData = new FormData(this);

    $.ajax({
        url: `<?php echo e(route("finance.sales.update-subscription", ":id")); ?>`.replace(':id', subscriptionId),
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                show_toastr('success', response.message);
                $('#editSubscriptionModal').modal('hide');
                location.reload();
            } else {
                show_toastr('error', response.message);
            }
        },
        error: function() {
            show_toastr('error', 'Failed to update subscription');
        }
    });
});

// ==================== INSTALLMENT PLAN FUNCTIONALITY ====================

// Installment plan customer search functionality
let installmentSearchTimeout;
let selectedInstallmentCustomer = null;

$('#installmentCustomerNameSearch').on('input', function() {
    const searchTerm = $(this).val();

    clearTimeout(installmentSearchTimeout);

    if (searchTerm.length < 2) {
        $('#installmentCustomerDropdown').removeClass('show').empty();
        return;
    }

    installmentSearchTimeout = setTimeout(function() {
        searchInstallmentCustomers(searchTerm);
    }, 300);
});

// Handle installment customer search
function searchInstallmentCustomers(searchTerm) {
    $.ajax({
        url: '<?php echo e(route("finance.sales.search-contacts")); ?>',
        type: 'GET',
        data: { search: searchTerm },
        success: function(response) {
            if (response.success) {
                displayInstallmentCustomerOptions(response.contacts);
            } else {
                $('#installmentCustomerDropdown').removeClass('show').empty();
            }
        },
        error: function() {
            $('#installmentCustomerDropdown').removeClass('show').empty();
        }
    });
}

// Display installment customer search results
function displayInstallmentCustomerOptions(contacts) {
    const dropdown = $('#installmentCustomerDropdown');
    dropdown.empty();

    if (contacts.length === 0) {
        dropdown.append('<div class="dropdown-item-text text-muted"><?php echo e(__("No contacts found")); ?></div>');
    } else {
        contacts.forEach(function(contact) {
            const item = $(`
                <a class="dropdown-item installment-customer-option" href="#"
                   data-id="${contact.id}"
                   data-type="${contact.type}"
                   data-name="${contact.name}"
                   data-email="${contact.email}"
                   data-phone="${contact.phone}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${contact.display_name}</strong>
                            <br>
                            <small class="text-muted">${contact.display_info}</small>
                        </div>
                        <span class="badge bg-${contact.type === 'customer' ? 'primary' : 'info'} ms-2">
                            ${contact.type === 'customer' ? '<?php echo e(__("Customer")); ?>' : '<?php echo e(__("Lead")); ?>'}
                        </span>
                    </div>
                </a>
            `);
            dropdown.append(item);
        });
    }

    dropdown.addClass('show');
}

// Handle installment customer selection
$(document).on('click', '.installment-customer-option', function(e) {
    e.preventDefault();

    const customerId = $(this).data('id');
    const customerType = $(this).data('type');
    const customerName = $(this).data('name');

    // Set selected customer
    selectedInstallmentCustomer = {
        id: customerId,
        type: customerType,
        name: customerName
    };

    // Update form fields
    $('#installmentCustomerNameSearch').val(customerName);
    $('#installmentCustomerName').val(customerId);
    $('#installmentCustomerType').val(customerType);
    $('#installmentCustomerDropdown').removeClass('show').empty();

    // Fetch detailed contact information
    fetchInstallmentContactDetails(customerType, customerId);
});

// Fetch detailed installment contact information
function fetchInstallmentContactDetails(type, id) {
    $.ajax({
        url: `<?php echo e(route("finance.sales.get-contact-details", ["type" => ":type", "id" => ":id"])); ?>`
            .replace(':type', type)
            .replace(':id', id),
        type: 'GET',
        success: function(response) {
            if (response.success) {
                populateInstallmentContactFields(response.contact);
            } else {
                show_toastr('error', response.message || '<?php echo e(__("Failed to fetch contact details")); ?>');
            }
        },
        error: function() {
            show_toastr('error', '<?php echo e(__("Failed to fetch contact details")); ?>');
        }
    });
}

// Populate installment email and phone fields
function populateInstallmentContactFields(contact) {
    // Clear and populate email dropdown
    const emailSelect = $('#installmentCustomerEmail');
    emailSelect.empty().append('<option value=""><?php echo e(__("Select Customer Email")); ?></option>');

    if (contact.emails && contact.emails.length > 0) {
        contact.emails.forEach(function(email) {
            if (email) {
                emailSelect.append(`<option value="${email}">${email}</option>`);
            }
        });
        // Auto-select first email
        emailSelect.val(contact.emails[0]);
    }

    // Clear and populate phone dropdown
    const phoneSelect = $('#installmentCustomerContact');
    phoneSelect.empty().append('<option value=""><?php echo e(__("Select Customer Contact")); ?></option>');

    if (contact.phones && contact.phones.length > 0) {
        contact.phones.forEach(function(phone) {
            if (phone) {
                phoneSelect.append(`<option value="${phone}">${phone}</option>`);
            }
        });
        // Auto-select first phone
        phoneSelect.val(contact.phones[0]);
    }

    // Update help text
    $('#installmentEmailHelpText').text(`<?php echo e(__("Loaded from")); ?> ${contact.type === 'customer' ? '<?php echo e(__("Customer")); ?>' : '<?php echo e(__("Lead")); ?>'}: ${contact.name}`);
    $('#installmentPhoneHelpText').text(`<?php echo e(__("Loaded from")); ?> ${contact.type === 'customer' ? '<?php echo e(__("Customer")); ?>' : '<?php echo e(__("Lead")); ?>'}: ${contact.name}`);
}

// Hide dropdown when clicking outside
$(document).on('click', function(e) {
    if (!$(e.target).closest('#installmentCustomerNameSearch, #installmentCustomerDropdown').length) {
        $('#installmentCustomerDropdown').removeClass('show');
    }
});

// Clear installment customer selection when search field is cleared
$('#installmentCustomerNameSearch').on('keyup', function() {
    if ($(this).val() === '') {
        selectedInstallmentCustomer = null;
        $('#installmentCustomerName').val('');
        $('#installmentCustomerType').val('');
        $('#installmentCustomerEmail').empty().append('<option value=""><?php echo e(__("Select Customer Email")); ?></option>');
        $('#installmentCustomerContact').empty().append('<option value=""><?php echo e(__("Select Customer Contact")); ?></option>');
        $('#installmentEmailHelpText').text('<?php echo e(__("Auto-populated from selected customer")); ?>');
        $('#installmentPhoneHelpText').text('<?php echo e(__("Auto-populated from selected customer")); ?>');
    }
});

// Load products when installment modal is opened
$('#createInstallmentModal').on('show.bs.modal', function() {
    loadInstallmentProducts();

    // Set default active date to today
    const today = new Date().toISOString().split('T')[0];
    $('#installmentActiveDate').val(today);
});

// Add product search functionality for installment
let installmentProductSearchTimeout;
$('#installmentProduct').on('focus', function() {
    if ($(this).find('option').length <= 1) {
        loadInstallmentProducts();
    }
});

// Add keyboard search for installment products
$('#installmentProduct').on('keyup', function(e) {
    if (e.key === 'Enter' || e.key === 'ArrowDown' || e.key === 'ArrowUp') return;

    clearTimeout(installmentProductSearchTimeout);
    const searchTerm = $(this).val();

    installmentProductSearchTimeout = setTimeout(() => {
        if (searchTerm.length >= 2) {
            searchInstallmentProducts(searchTerm);
        } else if (searchTerm.length === 0) {
            loadInstallmentProducts();
        }
    }, 300);
});

// Search products for installment
function searchInstallmentProducts(searchTerm) {
    $.ajax({
        url: '<?php echo e(route("invoice.products.dropdown")); ?>',
        type: 'GET',
        data: { search: searchTerm },
        success: function(response) {
            if (response.status) {
                populateInstallmentProductDropdown(response.data);
            }
        }
    });
}

// Load products for installment dropdown with enhanced loading state
function loadInstallmentProducts() {
    const select = $('.installment-product-select');
    select.html('<option value=""><?php echo e(__("Loading products...")); ?></option>');

    $.ajax({
        url: '<?php echo e(route("invoice.products.dropdown")); ?>',
        type: 'GET',
        success: function(response) {
            if (response.status) {
                populateInstallmentProductDropdown(response.data);
                if (response.data.length === 0) {
                    select.html('<option value=""><?php echo e(__("No products available")); ?></option>');
                    show_toastr('warning', '<?php echo e(__("No products found. Please add products first.")); ?>');
                }
            } else {
                select.html('<option value=""><?php echo e(__("Failed to load products")); ?></option>');
                show_toastr('error', response.message || '<?php echo e(__("Failed to load products")); ?>');
            }
        },
        error: function() {
            select.html('<option value=""><?php echo e(__("Error loading products")); ?></option>');
            show_toastr('error', '<?php echo e(__("Error loading products")); ?>');
        }
    });
}

// Populate installment product dropdown with enhanced data
function populateInstallmentProductDropdown(products) {
    const select = $('.installment-product-select');
    select.html('<option value=""><?php echo e(__("Select Product")); ?></option>');

    products.forEach(product => {
        const displayName = product.name + (product.sku ? ` (${product.sku})` : '');
        const priceDisplay = product.sale_price ? ` - <?php echo e(\Auth::user()->currencySymbol()); ?>${parseFloat(product.sale_price).toFixed(2)}` : '';

        select.append(`<option value="${product.id}"
            data-price="${product.sale_price || 0}"
            data-name="${product.name}"
            data-sku="${product.sku || ''}"
            data-description="${product.description || ''}"
            data-category="${product.category || ''}"
            data-type="${product.type || ''}"
            data-table-source="${product.table_source || ''}"
            >${displayName}${priceDisplay}</option>`);
    });
}

// Enhanced installment product selection handling
$('#installmentProduct').on('change', function() {
    const selectedOption = $(this).find('option:selected');
    const productId = $(this).val();
    const price = selectedOption.data('price') || 0;
    const name = selectedOption.data('name') || '';
    const description = selectedOption.data('description') || '';

    if (productId) {
        // Auto-populate price
        $('#installmentProductPrice').val(parseFloat(price).toFixed(2));

        // Auto-populate invoice description if empty
        if (!$('#installmentInvoiceDescription').val() && description) {
            // Check if Summernote is initialized
            if (typeof $.fn.summernote !== 'undefined' && $('#installmentInvoiceDescription').hasClass('note-editable')) {
                $('#installmentInvoiceDescription').summernote('code', description);
            } else {
                $('#installmentInvoiceDescription').val(description);
            }
        }

        // Calculate installment balance
        calculateInstallmentBalance();

        // Load detailed product information
        loadProductDetails(productId, 'installment');

        // Show success message
        show_toastr('success', `<?php echo e(__("Product selected")); ?>: ${name}`);
    } else {
        // Clear fields when no product selected
        $('#installmentProductPrice').val('');
        if (typeof $.fn.summernote !== 'undefined' && $('#installmentInvoiceDescription').hasClass('note-editable')) {
            $('#installmentInvoiceDescription').summernote('reset');
        } else {
            $('#installmentInvoiceDescription').val('');
        }
        hideProductDetails('installment');
    }
});

// Calculate installment balance when values change
$('#installmentProductPrice, #installmentProductQty, #installmentDownPayment, #installmentDiscountAmount').on('input', calculateInstallmentBalance);

function calculateInstallmentBalance() {
    const productPrice = parseFloat($('#installmentProductPrice').val()) || 0;
    const quantity = parseInt($('#installmentProductQty').val()) || 1;
    const downPayment = parseFloat($('#installmentDownPayment').val()) || 0;
    const discountAmount = parseFloat($('#installmentDiscountAmount').val()) || 0;

    const totalAmount = (productPrice * quantity) - discountAmount;
    const balance = totalAmount - downPayment;

    $('#installmentBalance').text(balance.toFixed(2));
}

// Dynamic installment addition
let installmentCounter = 0;

$('#addInstallmentBtn').on('click', function() {
    installmentCounter++;
    const ordinalSuffix = getOrdinalSuffix(installmentCounter);
    const installmentHtml = `
        <div class="installment-item border rounded p-3 mb-3" data-installment="${installmentCounter}">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">${installmentCounter}${ordinalSuffix} <?php echo e(__('Installment')); ?></h6>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-success btn-sm" onclick="addAnotherInstallment()">
                        <?php echo e(__('Add Installment')); ?>

                    </button>
                    ${installmentCounter > 1 ? '<button type="button" class="btn btn-danger btn-sm" onclick="removeInstallment(this)"><i class="ti ti-trash"></i></button>' : ''}
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label"><?php echo e(__('Payment Date')); ?></label>
                    <input type="date" class="form-control installment-date" name="installments[${installmentCounter-1}][payment_date]" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label"><?php echo e(__('Amount')); ?></label>
                    <input type="number" step="0.01" class="form-control installment-amount" name="installments[${installmentCounter-1}][amount]" placeholder="0" required>
                </div>
            </div>
        </div>
    `;

    $('#installmentsList').append(installmentHtml);
    validateInstallmentAmounts();
});

function getOrdinalSuffix(num) {
    const j = num % 10;
    const k = num % 100;
    if (j == 1 && k != 11) {
        return "st";
    }
    if (j == 2 && k != 12) {
        return "nd";
    }
    if (j == 3 && k != 13) {
        return "rd";
    }
    return "th";
}

function removeInstallment(button) {
    $(button).closest('.installment-item').remove();
    reindexInstallments();
    validateInstallmentAmounts();
}

function reindexInstallments() {
    $('.installment-item').each(function(index) {
        const newIndex = index + 1;
        const ordinalSuffix = getOrdinalSuffix(newIndex);

        $(this).attr('data-installment', newIndex);
        $(this).find('h6').text(`${newIndex}${ordinalSuffix} <?php echo e(__('Installment')); ?>`);
        $(this).find('.installment-date').attr('name', `installments[${index}][payment_date]`);
        $(this).find('.installment-amount').attr('name', `installments[${index}][amount]`);
    });

    installmentCounter = $('.installment-item').length;
}

function addAnotherInstallment() {
    $('#addInstallmentBtn').click();
}

// Handle installment form submission
$('#createInstallmentForm').on('submit', function(e) {
    e.preventDefault();

    // Validate customer selection
    if (!selectedInstallmentCustomer || !$('#installmentCustomerName').val()) {
        show_toastr('error', '<?php echo e(__("Please select a customer")); ?>');
        $('#installmentCustomerNameSearch').addClass('is-invalid');
        return;
    }

    // Validate installments
    const installments = $('.installment-item');
    if (installments.length === 0) {
        show_toastr('error', '<?php echo e(__("Please add at least one installment")); ?>');
        return;
    }

    // Get Summernote content
    if (typeof $.fn.summernote !== 'undefined') {
        const summernoteContent = $('#installmentInvoiceDescription').summernote('code');
        $('#installmentInvoiceDescription').val(summernoteContent);
    }

    const formData = new FormData(this);
    const submitBtn = $(this).find('button[type="submit"]');
    const originalText = submitBtn.text();

    submitBtn.prop('disabled', true).text('<?php echo e(__("Creating...")); ?>');

    $.ajax({
        url: '<?php echo e(route("finance.sales.store-installment-plan")); ?>',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                show_toastr('success', response.message);
                $('#createInstallmentModal').modal('hide');
                resetInstallmentForm();
                location.reload(); // Refresh to show new installment plan
            } else {
                show_toastr('error', response.message || '<?php echo e(__("Something went wrong")); ?>');
            }
        },
        error: function(xhr) {
            let errorMessage = '<?php echo e(__("Something went wrong")); ?>';

            // Clear previous validation errors
            $('.form-control, #installmentCustomerNameSearch').removeClass('is-invalid');
            $('.invalid-feedback').text('');

            if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
                const errors = xhr.responseJSON.errors;
                Object.keys(errors).forEach(function(field) {
                    let input = $(`[name="${field}"]`);

                    // Handle customer_id field specially
                    if (field === 'customer_id') {
                        input = $('#installmentCustomerNameSearch');
                    }

                    if (input.length > 0) {
                        input.addClass('is-invalid');
                        const feedback = input.siblings('.invalid-feedback');
                        if (feedback.length > 0) {
                            feedback.text(errors[field][0]);
                        }
                    }
                });
                errorMessage = '<?php echo e(__("Please check the form for errors")); ?>';
            }

            show_toastr('error', errorMessage);
        },
        complete: function() {
            submitBtn.prop('disabled', false).text(originalText);
        }
    });
});

function resetInstallmentForm() {
    $('#createInstallmentForm')[0].reset();
    selectedInstallmentCustomer = null;
    installmentCounter = 0;

    // Reset customer fields
    $('#installmentCustomerNameSearch').val('');
    $('#installmentCustomerName').val('');
    $('#installmentCustomerType').val('');
    $('#installmentCustomerEmail').empty().append('<option value=""><?php echo e(__("Select Customer Email")); ?></option>');
    $('#installmentCustomerContact').empty().append('<option value=""><?php echo e(__("Select Customer Contact")); ?></option>');
    $('#installmentEmailHelpText').text('<?php echo e(__("Auto-populated from selected customer")); ?>');
    $('#installmentPhoneHelpText').text('<?php echo e(__("Auto-populated from selected customer")); ?>');

    // Reset product fields
    $('#installmentProduct').html('<option value=""><?php echo e(__("Select Product")); ?></option>');
    $('#installmentProductPrice').val('');
    hideProductDetails('installment');

    // Reset installment fields
    $('#installmentsList').empty();
    $('#installmentBalance').text('0');

    // Clear validation states
    $('.form-control, #installmentCustomerNameSearch').removeClass('is-invalid');
    $('.invalid-feedback').text('');
    $('#installment-validation-message').remove();

    // Reset Summernote
    if (typeof $.fn.summernote !== 'undefined') {
        $('#installmentInvoiceDescription').summernote('reset');
    } else {
        $('#installmentInvoiceDescription').val('');
    }

    // Reload products for next use
    loadInstallmentProducts();
}

// ==================== INSTALLMENT PLAN ACTION FUNCTIONS ====================

function cancelInstallmentPlan(planId) {
    if (confirm('<?php echo e(__("Are you sure you want to cancel this installment plan?")); ?>')) {
        $.ajax({
            url: `<?php echo e(route("finance.sales.cancel-installment-plan", ":id")); ?>`.replace(':id', planId),
            type: 'POST',
            data: {
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                if (response.success) {
                    show_toastr('success', response.message);
                    location.reload();
                } else {
                    show_toastr('error', response.message);
                }
            },
            error: function() {
                show_toastr('error', '<?php echo e(__("Failed to cancel installment plan")); ?>');
            }
        });
    }
}

function showInstallmentPlanNotes(planId) {
    // Load existing notes
    $.ajax({
        url: `<?php echo e(route("finance.sales.get-installment-plan", ":id")); ?>`.replace(':id', planId),
        type: 'GET',
        success: function(response) {
            if (response.success) {
                $('#notesInstallmentPlanId').val(planId);
                $('#installmentPlanNotes').val(response.installment_plan.notes || '');
                $('#installmentPlanNotesModal').modal('show');
            }
        }
    });
}

function saveInstallmentPlanNotes() {
    const planId = $('#notesInstallmentPlanId').val();
    const notes = $('#installmentPlanNotes').val();

    $.ajax({
        url: `<?php echo e(route("finance.sales.update-installment-plan-notes", ":id")); ?>`.replace(':id', planId),
        type: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>',
            notes: notes
        },
        success: function(response) {
            if (response.success) {
                show_toastr('success', response.message);
                $('#installmentPlanNotesModal').modal('hide');
            } else {
                show_toastr('error', response.message);
            }
        },
        error: function() {
            show_toastr('error', '<?php echo e(__("Failed to save notes")); ?>');
        }
    });
}

function editInstallmentPlan(planId) {
    // Load installment plan data
    $.ajax({
        url: `<?php echo e(route("finance.sales.get-installment-plan", ":id")); ?>`.replace(':id', planId),
        type: 'GET',
        success: function(response) {
            if (response.success) {
                const plan = response.installment_plan;
                $('#editInstallmentPlanId').val(planId);
                $('#editInstallmentStatus').val(plan.status);
                $('#editInstallmentNextDate').val(plan.next_installment_date);
                $('#editInstallmentPaidAmount').val(plan.paid_amount);
                $('#editInstallmentPendingAmount').val(plan.pending_amount);
                $('#editInstallmentReceiptUrl').val(plan.receipt_url);
                $('#editInstallmentPlanModal').modal('show');
            }
        }
    });
}

function deleteInstallmentPlan(planId) {
    if (confirm('<?php echo e(__("Are you sure you want to delete this installment plan? This action cannot be undone.")); ?>')) {
        $.ajax({
            url: `<?php echo e(route("finance.sales.delete-installment-plan", ":id")); ?>`.replace(':id', planId),
            type: 'DELETE',
            data: {
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                if (response.success) {
                    show_toastr('success', response.message);
                    location.reload();
                } else {
                    show_toastr('error', response.message);
                }
            },
            error: function() {
                show_toastr('error', '<?php echo e(__("Failed to delete installment plan")); ?>');
            }
        });
    }
}

// Handle edit installment plan form submission
$(document).on('submit', '#editInstallmentPlanForm', function(e) {
    e.preventDefault();

    const planId = $('#editInstallmentPlanId').val();
    const formData = new FormData(this);

    $.ajax({
        url: `<?php echo e(route("finance.sales.update-installment-plan", ":id")); ?>`.replace(':id', planId),
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                show_toastr('success', response.message);
                $('#editInstallmentPlanModal').modal('hide');
                location.reload();
            } else {
                show_toastr('error', response.message);
            }
        },
        error: function() {
            show_toastr('error', 'Failed to update installment plan');
        }
    });
});

// Initialize Summernote for invoice description and other components
$(document).ready(function() {
    // Initialize Summernote if available
    if (typeof $.fn.summernote !== 'undefined') {
        $('#installmentInvoiceDescription').summernote({
            placeholder: "Enter your content...",
            tabsize: 2,
            height: 150,
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'italic', 'underline', 'strikethrough']],
                ['fontname', ['fontname']],
                ['fontsize', ['fontsize']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['link']],
                ['view', ['fullscreen', 'codeview']]
            ]
        });
    }

    // Initialize Bootstrap tooltips
    if (typeof bootstrap !== 'undefined') {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // Add loading animation to product selects
    $('.product-select').on('focus', function() {
        $(this).addClass('loading');
    }).on('blur', function() {
        $(this).removeClass('loading');
    });
});

// Reset installment modal when it's closed
$('#createInstallmentModal').on('hidden.bs.modal', function() {
    resetInstallmentForm();
});

// Add validation for installment amounts
$(document).on('input', '.installment-amount', function() {
    validateInstallmentAmounts();
});

function validateInstallmentAmounts() {
    const balance = parseFloat($('#installmentBalance').text()) || 0;
    let totalInstallmentAmount = 0;

    $('.installment-amount').each(function() {
        const amount = parseFloat($(this).val()) || 0;
        totalInstallmentAmount += amount;
    });

    const difference = Math.abs(balance - totalInstallmentAmount);

    if (difference > 0.01 && $('.installment-amount').length > 0) {
        $('.installment-amount').addClass('is-invalid');
        if ($('#installment-validation-message').length === 0) {
            $('#installmentsList').append('<div id="installment-validation-message" class="alert alert-warning mt-2"><?php echo e(__("Total installment amounts must equal the balance amount")); ?></div>');
        }
    } else {
        $('.installment-amount').removeClass('is-invalid');
        $('#installment-validation-message').remove();
    }
}

// Auto-calculate installment amounts when frequency changes
$('#installmentPaymentFrequency').on('change', function() {
    const frequency = $(this).val();
    const balance = parseFloat($('#installmentBalance').text()) || 0;
    const installmentCount = $('.installment-item').length;

    if (installmentCount > 0 && balance > 0) {
        const amountPerInstallment = (balance / installmentCount).toFixed(2);
        $('.installment-amount').val(amountPerInstallment);
        validateInstallmentAmounts();
    }
});

// Auto-set dates based on frequency
$(document).on('change', '.installment-date', function() {
    const currentDate = new Date($(this).val());
    const frequency = $('#installmentPaymentFrequency').val();
    const currentItem = $(this).closest('.installment-item');
    const nextItem = currentItem.next('.installment-item');

    if (nextItem.length > 0) {
        let nextDate = new Date(currentDate);

        switch (frequency) {
            case 'weekly':
                nextDate.setDate(nextDate.getDate() + 7);
                break;
            case 'monthly':
                nextDate.setMonth(nextDate.getMonth() + 1);
                break;
            case 'quarterly':
                nextDate.setMonth(nextDate.getMonth() + 3);
                break;
            case 'yearly':
                nextDate.setFullYear(nextDate.getFullYear() + 1);
                break;
        }

        const nextDateString = nextDate.toISOString().split('T')[0];
        nextItem.find('.installment-date').val(nextDateString);
    }
});
</script>
<?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/finance/tabs/sales.blade.php ENDPATH**/ ?>